<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON><PERSON> - <PERSON>A Student, Web & Software Developer, Tech Enthusiast">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, portfolio, web developer, software developer, BCA student">
    <title><PERSON><PERSON><PERSON> | Portfolio</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/profile.png">

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/skills-projects.css">
    <link rel="stylesheet" href="css/lightbox.css">
    <link rel="stylesheet" href="css/achievements.css">
    <link rel="stylesheet" href="css/certificate-gallery.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Lottie Player for Jellyfish Animation -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
</head>
<body>
    <!-- Particle Background -->
    <div id="particles-js"></div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <div class="logo">
                    <a href="#home">
                        <h1>Akash<span>.</span></h1>
                    </a>
                </div>

                <div class="nav-container">
                    <ul class="nav-links">
                        <li><a href="#home" class="nav-link active"><span>Home</span></a></li>
                        <li><a href="#about" class="nav-link"><span>About</span></a></li>
                        <li><a href="#skills" class="nav-link"><span>Skills</span></a></li>
                        <li><a href="#projects" class="nav-link"><span>Projects</span></a></li>
                        <li><a href="#achievements" class="nav-link"><span>Achievements</span></a></li>
                        <li><a href="#contact" class="nav-link"><span>Contact</span></a></li>
                    </ul>
                </div>

                <button class="hamburger" aria-label="Toggle menu">
                    <span class="hamburger-box">
                        <span class="hamburger-inner"></span>
                    </span>
                </button>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Hi, I'm <span>Akash Prajapati</span></h1>
                <h2>I'm a <span class="typed-text"></span></h2>
                <p>Passionate about creating innovative digital solutions and exploring new technologies.</p>
                <div class="cta-buttons">
                    <a href="#projects" class="btn primary-btn">View Projects</a>
                    <a href="#contact" class="btn secondary-btn">Contact Me</a>
                </div>
            </div>
            <div class="hero-animation">
                <!-- Jellyfish Animation Container -->
                <div id="jellyfish-container">
                    <lottie-player id="jellyfish" src="assets/animations/jellyfish.json" background="transparent" speed="1" loop autoplay></lottie-player>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2>About <span>Me</span></h2>
                <div class="underline"></div>
            </div>

            <div class="about-wrapper">
                <!-- Profile Card -->
                <div class="profile-card">
                    <div class="profile-card-header">
                        <div class="profile-img">
                            <img src="assets/images/akash-prajapati.jpg" alt="Akash Prajapati">
                        </div>
                        <div class="profile-name-wrapper">
                            <h3 class="profile-name">Akash Prajapati</h3>
                            <p class="profile-title">BCA Student & Web Developer</p>
                        </div>
                    </div>

                    <div class="profile-stats">
                        <div class="stat-item">
                            <span class="stat-number">4+</span>
                            <span class="stat-label">Projects</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">5+</span>
                            <span class="stat-label">Certifications</span>
                        </div>
                    </div>

                    <div class="profile-social">
                        <a href="https://github.com/akashprajapati1232" target="_blank" class="social-btn github"><i class="fab fa-github"></i></a>
                        <a href="https://www.linkedin.com/in/akash-prajapati1232/" target="_blank" class="social-btn linkedin"><i class="fab fa-linkedin-in"></i></a>
                        <a href="https://www.instagram.com/itz_prabhas43/" target="_blank" class="social-btn instagram"><i class="fab fa-instagram"></i></a>
                        <a href="mailto:<EMAIL>" class="social-btn email"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <!-- About Content -->
                <div class="about-content">
                    <div class="about-tabs">
                        <button class="tab-btn active" data-tab="bio">Bio</button>
                        <button class="tab-btn" data-tab="education">Education</button>
                        <button class="tab-btn" data-tab="certifications">Certifications</button>
                    </div>

                    <div class="tab-content">
                        <div class="tab-pane active" id="bio">
                            <h3 class="content-title"><i class="fas fa-user"></i> About Me</h3>
                            <p>Aspiring web and software developer with a passion for creating innovative digital solutions. Currently pursuing a Bachelor's degree in Computer Applications, I aim to leverage my technical skills and creativity to develop user-friendly applications that solve real-world problems.</p>
                            <p>I specialize in frontend development with a growing expertise in backend technologies. My goal is to create seamless, responsive, and accessible web experiences that make a positive impact.</p>

                            <div class="key-skills">
                                <h4>Key Skills</h4>
                                <div class="skill-tags">
                                    <span class="skill-tag">HTML/CSS</span>
                                    <span class="skill-tag">JavaScript</span>
                                    <span class="skill-tag">React</span>
                                    <span class="skill-tag">Node.js</span>
                                    <span class="skill-tag">Python</span>
                                    <span class="skill-tag">UI/UX</span>
                                    <span class="skill-tag">WordPress</span>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane" id="education">
                            <h3 class="content-title"><i class="fas fa-graduation-cap"></i> Education</h3>
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-dot"></div>
                                    <div class="timeline-content">
                                        <h4>Bachelor of Computer Applications (BCA)</h4>
                                        <p class="timeline-date">2023 - Present</p>
                                        <p>Pursuing Bachelor of Computer Application (B.C.A) from Maa Shakumbhari University, Saharanpur.</p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-dot"></div>
                                    <div class="timeline-content">
                                        <h4>Higher Secondary Education (12th)</h4>
                                        <p class="timeline-date">2023</p>
                                        <p>12th passed with Science from U.P. Board in the year 2023.</p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-dot"></div>
                                    <div class="timeline-content">
                                        <h4>Secondary Education (10th)</h4>
                                        <p class="timeline-date">2021</p>
                                        <p>10th passed with Science from U.P. Board in the year 2021.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane" id="certifications">
                            <h3 class="content-title"><i class="fas fa-certificate"></i> Certifications</h3>
                            <div class="cert-grid">
                                <div class="cert-item">
                                    <div class="cert-icon"><i class="fab fa-html5"></i></div>
                                    <div class="cert-info">
                                        <h4>Web Development Fundamentals</h4>
                                        <p>HTML, CSS, and JavaScript basics</p>
                                    </div>
                                </div>
                                <div class="cert-item">
                                    <div class="cert-icon"><i class="fab fa-js"></i></div>
                                    <div class="cert-info">
                                        <h4>JavaScript Programming</h4>
                                        <p>Advanced concepts and frameworks</p>
                                    </div>
                                </div>
                                <div class="cert-item">
                                    <div class="cert-icon"><i class="fab fa-python"></i></div>
                                    <div class="cert-info">
                                        <h4>Python Programming</h4>
                                        <p>Core concepts and applications</p>
                                    </div>
                                </div>
                                <div class="cert-item">
                                    <div class="cert-icon"><i class="fas fa-laptop-code"></i></div>
                                    <div class="cert-info">
                                        <h4>ADCA Certification</h4>
                                        <p>Advanced Diploma in Computer Applications</p>
                                        <a href="#" id="adcaCertificatesLink" class="cert-link">View Certificate</a>
                                    </div>
                                </div>
                            </div>
                            <div class="cert-view-all">
                                <button class="view-all-certificates"><i class="fas fa-certificate"></i> View All Certificates</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <div class="section-header">
                <h2>My <span>Skills</span></h2>
                <div class="underline"></div>
            </div>

            <div class="skills-intro">
                <p>I specialize in creating modern, responsive web applications with a focus on clean code and user experience. Here's my technical toolkit:</p>
            </div>

            <div class="skills-tabs">
                <button class="skill-tab active" data-skill="all">All Skills</button>
                <button class="skill-tab" data-skill="programming">Programming</button>
                <button class="skill-tab" data-skill="web">Web Development</button>
                <button class="skill-tab" data-skill="frameworks">Frameworks</button>
                <button class="skill-tab" data-skill="tools">Tools & Others</button>
            </div>

            <div class="skills-grid">
                <!-- Programming Skills -->
                <div class="skill-card" data-category="programming">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <span>C</span>
                        </div>
                        <h4>C</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 80%"></div>
                        </div>
                        <span class="skill-percent">80%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="programming">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <span>C++</span>
                        </div>
                        <h4>C++</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 75%"></div>
                        </div>
                        <span class="skill-percent">75%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="programming">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-python"></i>
                        </div>
                        <h4>Python</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 85%"></div>
                        </div>
                        <span class="skill-percent">85%</span>
                    </div>
                </div>

                <!-- Web Development Skills -->
                <div class="skill-card" data-category="web">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-html5"></i>
                        </div>
                        <h4>HTML5</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 95%"></div>
                        </div>
                        <span class="skill-percent">95%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="web">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-css3-alt"></i>
                        </div>
                        <h4>CSS3</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 90%"></div>
                        </div>
                        <span class="skill-percent">90%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="web">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-js"></i>
                        </div>
                        <h4>JavaScript</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 85%"></div>
                        </div>
                        <span class="skill-percent">85%</span>
                    </div>
                </div>

                <!-- Frameworks -->
                <div class="skill-card" data-category="frameworks">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-react"></i>
                        </div>
                        <h4>React</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 80%"></div>
                        </div>
                        <span class="skill-percent">80%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="frameworks">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-node-js"></i>
                        </div>
                        <h4>Node.js</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 75%"></div>
                        </div>
                        <span class="skill-percent">75%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="frameworks">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h4>MySQL</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 85%"></div>
                        </div>
                        <span class="skill-percent">85%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="frameworks">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h4>MongoDB</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 70%"></div>
                        </div>
                        <span class="skill-percent">70%</span>
                    </div>
                </div>

                <!-- Tools & Others -->
                <div class="skill-card" data-category="tools">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-git-alt"></i>
                        </div>
                        <h4>Git</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 85%"></div>
                        </div>
                        <span class="skill-percent">85%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="tools">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-github"></i>
                        </div>
                        <h4>GitHub</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 90%"></div>
                        </div>
                        <span class="skill-percent">90%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="tools">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4>Canva</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 80%"></div>
                        </div>
                        <span class="skill-percent">80%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="tools">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fa-solid fa-image"></i>
                        </div>
                        <h4>Photoshop</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 75%"></div>
                        </div>
                        <span class="skill-percent">75%</span>
                    </div>
                </div>

                <div class="skill-card" data-category="tools">
                    <div class="skill-card-inner">
                        <div class="skill-icon">
                            <i class="fab fa-wordpress"></i>
                        </div>
                        <h4>WordPress</h4>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 85%"></div>
                        </div>
                        <span class="skill-percent">85%</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <div class="section-header">
                <h2>My <span>Projects</span></h2>
                <div class="underline"></div>
            </div>

            <div class="projects-intro">
                <p>Here are some of my recent projects that showcase my skills and passion for web development.</p>
                <div class="projects-filter">
                    <button class="filter-btn active" data-filter="all">All Projects</button>
                    <button class="filter-btn" data-filter="web">Web Apps</button>
                    <button class="filter-btn" data-filter="game">Games</button>
                    <button class="filter-btn" data-filter="other">Other</button>
                </div>
            </div>

            <div class="projects-showcase">
                <!-- Project 1 -->
                <div class="project-item" data-type="web">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="assets/images/ImgNinja/project-ImgNinja-01.png" alt="ImgNinja Screenshot" class="project-img">
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="https://github.com/akashprajapati1232/ImgNinja" target="_blank" class="project-link"><i class="fab fa-github"></i></a>
                                    <a href="https://imagecompressor.42web.io/" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <div class="project-tags">
                                <span class="project-tag">HTML</span>
                                <span class="project-tag">CSS</span>
                                <span class="project-tag">JavaScript</span>
                            </div>
                            <h3 class="project-title">ImgNinja – All-in-One Image & File Handling Tool</h3>
                            <p class="project-description">A web application – an all-in-one online tool designed to simplify image and file handling with a modern, fast, and user-friendly interface.</p>
                            <div class="project-footer">
                                <span class="project-date">2025</span>
                                <a href="#" class="project-details-btn" data-project="imgninja">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 2: Snake Game -->
                <div class="project-item" data-type="game">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="assets/images/snake-game/04-new-version-snake-game.png" alt="Snake Game Screenshot" class="project-img">
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="https://github.com/akashprajapati1232/snake-game" target="_blank" class="project-link"><i class="fab fa-github"></i></a>
                                    <a href="3" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <div class="project-tags">
                                <span class="project-tag">HTML</span>
                                <span class="project-tag">CSS</span>
                                <span class="project-tag">JavaScript</span>
                            </div>
                            <h3 class="project-title">Snake Game</h3>
                            <p class="project-description">A simple Snake game created using HTML, CSS, and JavaScript. Currently has basic features with plans to add advanced functionality in the future.</p>
                            <div class="project-footer">
                                <span class="project-date">2025</span>
                                <a href="#" class="project-details-btn" data-project="snake">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 3: Portfolio Website -->
                <div class="project-item" data-type="other">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="assets/images/portfolio/01-portfolio.png" alt="Portfolio Screenshot" class="project-img">
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="https://github.com/akashprajapati1232/my-portfolio" target="_blank" class="project-link"><i class="fab fa-github"></i></a>
                                    <a href="https://akashprajapati.rf.gd/" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <div class="project-tags">
                                <span class="project-tag">HTML</span>
                                <span class="project-tag">CSS</span>
                                <span class="project-tag">JavaScript</span>
                                <span class="project-tag">Responsive</span>
                            </div>
                            <h3 class="project-title">Personal Portfolio Website</h3>
                            <p class="project-description">My personal portfolio website showcasing my skills, projects, and achievements. Built with modern web technologies and featuring a clean, responsive design.</p>
                            <div class="project-footer">
                                <span class="project-date">2025</span>
                                <a href="#" class="project-details-btn" data-project="portfolio">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Project Modal (Hidden by default) -->
            <div class="project-modal" id="projectModal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <div class="modal-body">
                        <!-- Content will be dynamically inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section id="achievements" class="achievements">
        <div class="container">
            <div class="section-header">
                <h2>My <span>Achievements</span></h2>
                <div class="underline"></div>
            </div>

            <div class="achievements-intro">
                <p>Here are some of my notable achievements and recognitions in the field of web development and programming.</p>
            </div>

            <div class="achievements-content">
                <!-- Achievement 1: Hackathon Participation -->
                <div class="achievement-card" data-tilt data-tilt-max="5" data-tilt-speed="400" data-tilt-glare data-tilt-max-glare="0.1">
                    <div class="achievement-header">
                        <div class="achievement-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="achievement-title">
                            <h3>Hackathon Participation</h3>
                            <span>Team Project</span>
                        </div>
                    </div>
                    <div class="achievement-body">
                        <p class="achievement-description">Participated in a 2-day hackathon and developed a QR-based Health Record System mobile app. The system allows doctors to scan a QR code from a patient's phone to instantly access their complete medical history and details.</p>
                        <div class="achievement-tags">
                            <span class="achievement-tag">Hackathon</span>
                            <span class="achievement-tag">Mobile App</span>
                            <span class="achievement-tag">Healthcare</span>
                            <span class="achievement-tag">QR Technology</span>
                        </div>
                    </div>
                    <div class="achievement-footer">
                        <div class="achievement-date">
                            <i class="far fa-calendar-alt"></i>
                            <span>Dec 2024</span>
                        </div>
                        <a href="#" class="achievement-link" data-project="hackathon">View Project <i class="fas fa-chevron-right"></i></a>
                    </div>
                </div>

                <!-- Achievement 2: Quiz Competition Winner -->
                <div class="achievement-card" data-tilt data-tilt-max="5" data-tilt-speed="400" data-tilt-glare data-tilt-max-glare="0.1">
                    <div class="achievement-badge bronze">
                        <i class="fas fa-award"></i>
                    </div>
                    <div class="achievement-header">
                        <div class="achievement-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div class="achievement-title">
                            <h3>Quiz Competition Winner</h3>
                            <span>3rd Place</span>
                        </div>
                    </div>
                    <div class="achievement-body">
                        <p class="achievement-description">Won third place in a technical quiz competition focused on programming and web development concepts, competing against 50+ participants.</p>
                        <div class="achievement-tags">
                            <span class="achievement-tag">Web Development</span>
                            <span class="achievement-tag">Programming</span>
                            <span class="achievement-tag">Competition</span>
                        </div>
                    </div>
                    <div class="achievement-footer">
                        <div class="achievement-date">
                            <i class="far fa-calendar-alt"></i>
                            <span>Nov 2023</span>
                        </div>
                        <a href="#" class="achievement-link" id="quizCertificateLink">View Certificate <i class="fas fa-chevron-right"></i></a>
                    </div>
                </div>

                <!-- Achievement 3: Web Development Certifications -->
                <div class="achievement-card" data-tilt data-tilt-max="5" data-tilt-speed="400" data-tilt-glare data-tilt-max-glare="0.1">
                    <div class="achievement-header">
                        <div class="achievement-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="achievement-title">
                            <h3>Web Development Certifications</h3>
                            <span>Multiple Courses</span>
                        </div>
                    </div>
                    <div class="achievement-body">
                        <p class="achievement-description">Completed multiple certifications in web development, including HTML, CSS, JavaScript, and responsive design. Continuously expanding knowledge through online courses and practical projects.</p>
                        <div class="achievement-tags">
                            <span class="achievement-tag">HTML/CSS</span>
                            <span class="achievement-tag">JavaScript</span>
                            <span class="achievement-tag">Responsive Design</span>
                        </div>
                    </div>
                    <div class="achievement-footer">
                        <div class="achievement-date">
                            <i class="far fa-calendar-alt"></i>
                            <span>2023 - 2025</span>
                        </div>
                        <a href="#" class="achievement-link" id="webDevCertificatesLink">View Certificates <i class="fas fa-chevron-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2>Contact <span>Me</span></h2>
                <div class="underline"></div>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h3>Phone</h3>
                            <p>+91 8115201583</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fab fa-github"></i>
                        <div>
                            <h3>GitHub</h3>
                            <p><a href="https://github.com/akashprajapati1232" target="_blank">github.com/akashprajapati1232</a></p>
                        </div>
                    </div>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/akash-prajapati1232/" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/itz_prabhas43/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contactForm">
                        <div class="form-group">
                            <input type="text" id="name" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" placeholder="Your Message" required></textarea>
                        </div>
                        <button type="submit" class="btn primary-btn">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Akash Prajapati. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@1.8.0/dist/vanilla-tilt.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/tilt.js"></script>
    <script src="js/typing.js"></script>
    <script src="js/tabs.js"></script>
    <script src="js/skills-projects.js"></script>
    <script src="js/lightbox.js"></script>
    <script src="js/certificate-gallery.js"></script>
    <script src="js/hackathon-gallery.js"></script>
</body>
</html>
