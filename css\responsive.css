/*
* Responsive Stylesheet for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* ===== LARGE DEVICES (Desktops, less than 1200px) ===== */
@media (max-width: 1199.98px) {
    .hero-content h1 {
        font-size: 3rem;
    }

    .hero-content h2 {
        font-size: 1.1rem;
    }

    .typed-text {
        min-width: 15ch;
    }

    #jellyfish-container {
        max-width: 550px;
        height: 550px;
    }

    .section-header h2 {
        font-size: 2.2rem;
    }
}

/* ===== MEDIUM DEVICES (Tablets, less than 992px) ===== */
@media (max-width: 991.98px) {
    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        margin-bottom: 50px;
    }

    .hero-content p {
        margin: 0 auto 30px;
    }

    .cta-buttons {
        justify-content: center;
    }

    #jellyfish-container {
        max-width: 500px;
        height: 500px;
    }

    .about-wrapper {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .profile-card {
        max-width: 400px;
        margin: 0 auto;
    }

    .skill-item {
        width: calc(50% - 10px);
    }

    .projects-content {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

/* ===== SMALL DEVICES (Landscape phones, less than 768px) ===== */
@media (max-width: 767.98px) {
    .nav-container {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        height: 100vh;
        background: rgba(10, 15, 30, 0.98);
        backdrop-filter: blur(15px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: all 0.5s cubic-bezier(0.77, 0.2, 0.05, 1.0);
        z-index: 1000;
        padding: 2rem;
        box-shadow: -5px 0 30px rgba(0, 0, 0, 0.3);
    }

    .nav-container.active {
        right: 0;
    }

    .nav-links {
        flex-direction: column;
        width: 100%;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .nav-link {
        width: 100%;
        text-align: center;
        padding: 1rem;
        font-size: 1.1rem;
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .about-tabs {
        flex-direction: column;
    }

    .tab-btn {
        padding: 12px;
    }

    .tab-content {
        padding: 20px;
    }

    .cert-grid {
        grid-template-columns: 1fr;
    }

    /* Social nav styles removed */

    .hamburger {
        display: block;
        z-index: 1001;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content h2 {
        font-size: 1rem;
    }

    .typed-text {
        min-width: 12ch;
        padding-right: 5px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    /* Skills Section Responsive */
    .skills-tabs {
        gap: 8px;
    }

    .skill-tab {
        padding: 8px 15px;
        font-size: 0.8rem;
    }

    .skills-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
    }

    .skill-card-inner {
        padding: 20px 15px;
    }

    .skill-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    /* Projects Section Responsive */
    .projects-filter {
        gap: 8px;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 0.8rem;
    }

    .projects-showcase {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    /* Achievement styles moved to achievements.css */

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-item i {
        margin-bottom: 10px;
    }

    .social-links {
        justify-content: center;
    }
}

/* ===== EXTRA SMALL DEVICES (Portrait phones, less than 576px) ===== */
@media (max-width: 575.98px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .typed-text {
        min-width: 10ch;
        font-size: 0.9rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .btn {
        width: 100%;
    }

    #jellyfish-container {
        max-width: 380px;
        height: 380px;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    /* Skills Section Extra Small */
    .skills-tabs {
        flex-direction: column;
        align-items: center;
    }

    .skill-tab {
        width: 100%;
        max-width: 200px;
        text-align: center;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    /* Projects Section Extra Small */
    .projects-filter {
        flex-direction: column;
        align-items: center;
    }

    .projects-filter::before,
    .projects-filter::after {
        display: none;
    }

    .filter-btn {
        width: 100%;
        max-width: 200px;
        text-align: center;
        padding: 8px 15px;
        font-size: 0.8rem;
    }

    .projects-showcase {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .project-card {
        max-width: 320px;
        margin: 0 auto;
        transform: none !important;
    }

    .project-card:hover {
        transform: none !important;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .project-image {
        height: 160px;
    }

    .project-img {
        object-position: center top;
    }

    .project-content {
        padding: 15px;
    }

    .project-tags {
        justify-content: center;
        gap: 6px;
        margin-bottom: 10px;
    }

    .project-tag {
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .project-title {
        font-size: 1.1rem;
        text-align: center;
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 8px;
    }

    .project-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .project-description {
        text-align: center;
        font-size: 0.85rem;
        margin-bottom: 15px;
        line-height: 1.5;
    }

    .project-footer {
        flex-direction: column;
        gap: 10px;
        align-items: center;
        padding-top: 12px;
    }

    .project-date {
        font-size: 0.8rem;
    }

    .project-details-btn {
        width: 100%;
        text-align: center;
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    }

    .gallery-img {
        height: 130px;
    }

    .project-links {
        flex-direction: column;
        gap: 10px;
    }

    .project-links .btn {
        width: 100%;
    }

    /* Achievement responsive styles moved to achievements.css */

    .contact-content {
        grid-template-columns: 1fr;
    }

    .form-group input,
    .form-group textarea {
        padding: 12px;
    }
}

/* ===== ANIMATIONS FOR RESPONSIVE ===== */
@media (max-width: 767.98px) {
    .card-inner {
        height: 300px;
    }

    .profile-img {
        width: 120px;
        height: 120px;
    }

    .profile-placeholder {
        font-size: 2.5rem;
    }

    .social-icons a {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Small Mobile Devices (375px - 425px) */
@media (min-width: 376px) and (max-width: 425px) {
    .project-card {
        max-width: 300px;
    }

    .project-image {
        height: 150px;
    }

    .project-img {
        object-position: center top;
        max-height: 150px;
    }

    .project-content {
        padding: 15px;
    }
}

/* Extra Small Devices (iPhone SE, etc.) */
@media (max-width: 375px) {
    .project-card {
        max-width: 280px;
    }

    .project-image {
        height: 140px;
    }

    .project-img {
        object-position: center top;
        max-height: 140px;
    }

    .project-content {
        padding: 12px;
    }

    .project-title {
        font-size: 1rem;
    }

    .project-description {
        font-size: 0.8rem;
        margin-bottom: 12px;
    }

    .project-tag {
        padding: 3px 6px;
        font-size: 0.65rem;
    }

    .project-details-btn {
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .project-date {
        font-size: 0.75rem;
    }
}

/* ===== PREFERS-REDUCED-MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
