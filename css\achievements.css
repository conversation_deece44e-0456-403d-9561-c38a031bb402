/*
* Achievements CSS for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* ===== ACHIEVEMENTS SECTION ===== */
.achievements {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

.achievements::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -5%;
    width: 40%;
    height: 70%;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.05), transparent 70%);
    z-index: -1;
    border-radius: 50%;
}

.achievements-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
}

.achievements-intro p {
    font-size: 1.1rem;
    color: var(--medium-text);
    line-height: 1.8;
}

/* Modern Achievement Cards Layout */
.achievements-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

/* Achievement Card */
.achievement-card {
    position: relative;
    background: rgba(30, 41, 59, 0.4);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.4s ease;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    flex-direction: column;
}

.achievement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(16, 185, 129, 0.2);
    background: rgba(30, 41, 59, 0.6);
}

/* Achievement Header */
.achievement-header {
    padding: 25px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--secondary), var(--secondary-light));
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

.achievement-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    animation: iconShine 3s infinite;
}

@keyframes iconShine {
    0% {
        transform: translateX(-100%);
    }
    20%, 100% {
        transform: translateX(100%);
    }
}

.achievement-title h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--light-text);
    margin-bottom: 5px;
}

.achievement-title span {
    display: inline-block;
    padding: 4px 10px;
    background: rgba(16, 185, 129, 0.1);
    color: var(--secondary-light);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Achievement Body */
.achievement-body {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.achievement-description {
    color: var(--medium-text);
    line-height: 1.7;
    margin-bottom: 20px;
    flex-grow: 1;
}

/* Achievement Footer */
.achievement-footer {
    padding: 0 25px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.achievement-date {
    font-size: 0.9rem;
    color: var(--medium-text);
    display: flex;
    align-items: center;
    gap: 5px;
}

.achievement-date i {
    color: var(--secondary);
    font-size: 0.8rem;
}

.achievement-link {
    font-size: 0.9rem;
    color: var(--secondary-light);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.achievement-link:hover {
    color: var(--secondary);
    transform: translateX(3px);
}

.achievement-link i {
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.achievement-link:hover i {
    transform: translateX(3px);
}

/* Achievement Badge */
.achievement-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 0.8rem;
    box-shadow: 0 4px 10px rgba(37, 99, 235, 0.3);
}

.achievement-badge.gold {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.achievement-badge.silver {
    background: linear-gradient(135deg, #9ca3af, #d1d5db);
}

.achievement-badge.bronze {
    background: linear-gradient(135deg, #b45309, #d97706);
}

/* Achievement Tags */
.achievement-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.achievement-tag {
    padding: 4px 10px;
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-light);
    border-radius: 20px;
    font-size: 0.8rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .achievements-content {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 576px) {
    .achievements-content {
        grid-template-columns: 1fr;
    }
    
    .achievement-header {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }
    
    .achievement-title h3 {
        font-size: 1.2rem;
    }
    
    .achievement-badge {
        top: 15px;
        right: 15px;
    }
    
    .achievement-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}
