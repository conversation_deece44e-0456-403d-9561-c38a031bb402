/*
* Main Stylesheet for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* ===== VARIABLES ===== */
:root {
    /* Modern Color Palette */
    --primary: #2563eb;         /* Vibrant blue */
    --primary-light: #60a5fa;   /* Light blue */
    --primary-dark: #1e40af;    /* Dark blue */
    --secondary: #10b981;       /* Emerald green */
    --secondary-light: #34d399; /* Light green */
    --accent: #f59e0b;          /* Amber */
    --accent-light: #fbbf24;    /* Light amber */
    --background-dark: #0f172a; /* Dark slate blue */
    --background-light: #1e293b; /* Slate blue */
    --card-bg: #1e293b;         /* Card background */
    --light-text: #f8fafc;      /* Almost white */
    --medium-text: #cbd5e1;     /* Light slate */
    --dark-text: #334155;       /* Dark slate */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --success: #10b981;         /* Green */
    --error: #ef4444;           /* Red */

    /* Fonts */
    --main-font: 'Poppins', sans-serif;
    --heading-font: 'Montserrat', sans-serif;

    /* Sizes */
    --container-width: 1200px;
    --section-spacing: 100px;
}

/* ===== GOOGLE FONTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Poppins:wght@300;400;500;600&display=swap');

/* ===== RESET ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--main-font);
    background: linear-gradient(135deg, var(--background-dark), var(--background-light));
    color: var(--light-text);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    background-attachment: fixed;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* ===== PARTICLES BACKGROUND ===== */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

/* ===== CONTAINER ===== */
.container {
    width: 90%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 15px;
}

/* ===== SECTION STYLES ===== */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-family: var(--heading-font);
    font-size: 2.5rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.section-header h2 span {
    color: var(--primary-light);
    position: relative;
}

/* Removed the span::after element that was creating the underline effect */

.underline {
    display: none; /* Hide the underline element */
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-block;
    padding: 12px 28px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: width 0.3s ease;
    z-index: -1;
}

.btn:hover::before {
    width: 100%;
}

.primary-btn {
    background: var(--primary);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.5);
    background: var(--primary-dark);
}

.secondary-btn {
    background: transparent;
    color: var(--light-text);
    border: 2px solid var(--secondary);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

.secondary-btn:hover {
    background: var(--secondary);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.small-btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 0;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    background: rgba(15, 23, 42, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.header.scrolled {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    background: rgba(10, 15, 30, 0.98);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    position: relative;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo h1 {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--light-text);
    transition: all 0.3s ease;
}

.logo:hover h1 {
    transform: translateY(-2px);
}

.logo span {
    color: var(--primary);
    font-weight: 700;
}

.nav-container {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 1.8rem;
}

.nav-link {
    position: relative;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    overflow: hidden;
}

.nav-link span {
    position: relative;
    z-index: 2;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease;
    border-radius: 6px;
    z-index: 1;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 0.1;
    transform: translateY(0);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-light);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    z-index: 2;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 30px;
}

.social-nav {
    display: flex;
    gap: 1rem;
    position: relative;
    padding-left: 1.5rem;
}

.social-nav::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    height: 24px;
    width: 1px;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateY(-50%);
}

.social-nav a {
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.social-nav a::after {
    display: none;
}

.social-nav a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-nav a[href*="github"]:hover {
    color: #fff;
    background: #333;
    border-color: #333;
}

.social-nav a[href*="linkedin"]:hover {
    color: #fff;
    background: #0077b5;
    border-color: #0077b5;
}

.social-nav a[href*="instagram"]:hover {
    color: #fff;
    background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
    border-color: #C13584;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    padding: 10px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    z-index: 1001;
}

.hamburger-box {
    width: 30px;
    height: 24px;
    display: inline-block;
    position: relative;
}

.hamburger-inner {
    display: block;
    top: 50%;
    margin-top: -2px;
}

.hamburger-inner,
.hamburger-inner::before,
.hamburger-inner::after {
    width: 30px;
    height: 2px;
    background-color: var(--light-text);
    border-radius: 4px;
    position: absolute;
    transition: transform 0.15s ease;
}

.hamburger-inner::before,
.hamburger-inner::after {
    content: "";
    display: block;
}

.hamburger-inner::before {
    top: -10px;
}

.hamburger-inner::after {
    bottom: -10px;
}

/* Hamburger Animation */
.hamburger.active .hamburger-inner {
    transform: rotate(45deg);
}

.hamburger.active .hamburger-inner::before {
    top: 0;
    opacity: 0;
}

.hamburger.active .hamburger-inner::after {
    bottom: 0;
    transform: rotate(-90deg);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 50%, rgba(37, 99, 235, 0.1), transparent 50%);
    z-index: -1;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1), transparent 50%);
    z-index: -1;
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.hero-content {
    flex: 1;
}

.hero-content h1 {
    font-family: var(--heading-font);
    font-size: 3.8rem;
    font-weight: 700;
    gap: 100px;
    margin-bottom: 15px;
    position: relative;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content h1 span {
    background: linear-gradient(45deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    display: inline-block;
    text-shadow: 0 5px 15px rgba(37, 99, 235, 0.2);
}

/* Removed the span::after element that was creating the underline effect in hero section */

.hero-content h2 {
    font-size: 1.8rem;
    font-weight: 500;
    margin-bottom: 20px;
    color: var(--light-text);
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.2s forwards;
    opacity: 0;
}

/* Typing animation styles */
.typed-text {
    color: var(--secondary);
    font-weight: 600;
    position: relative;
    display: inline-block;
    white-space: nowrap; /* Prevent text from wrapping */
    min-width: 18ch; /* Container width to fit the longest text */
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 35px;
    max-width: 600px;
    color: var(--medium-text);
    line-height: 1.8;
    animation: fadeInUp 1s ease-out 0.4s forwards;
    opacity: 0;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    animation: fadeInUp 1s ease-out 0.6s forwards;
    opacity: 0;
}

.hero-animation {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

#jellyfish-container {
    width: 100%;
    max-width: 600px;
    height: 600px;
    position: relative;
    animation: float 6s ease-in-out infinite, fadeIn 1.5s ease-out forwards;
    opacity: 0;
}

@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===== GLASSMORPHISM CARD ===== */
.glass-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(96, 165, 250, 0.1);
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.glass-card:hover {
    box-shadow: 0 12px 40px rgba(37, 99, 235, 0.15);
    transform: translateY(-5px);
    border-color: rgba(96, 165, 250, 0.2);
}

/* ===== FOOTER ===== */
footer {
    background: rgba(15, 23, 42, 0.95);
    padding: 30px 0;
    text-align: center;
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

footer p {
    font-size: 0.9rem;
    color: var(--medium-text);
}

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary);
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.scroll-top.active {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.5);
    background: var(--primary-dark);
}
