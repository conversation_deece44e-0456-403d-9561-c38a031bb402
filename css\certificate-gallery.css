/*
* Certificate Gallery CSS for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* Certificate Gallery Container */
.certificate-gallery-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.certificate-gallery-container.active {
    opacity: 1;
    visibility: visible;
}

/* Certificate Gallery Content */
.certificate-gallery-content {
    position: relative;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    background: var(--background-dark);
    border-radius: 12px;
    padding: 30px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.certificate-gallery-container.active .certificate-gallery-content {
    transform: scale(1);
}

/* Certificate Gallery Header */
.certificate-gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding-bottom: 15px;
}

.certificate-gallery-title {
    font-size: 1.5rem;
    color: var(--light-text);
    font-weight: 600;
}

.certificate-gallery-close {
    background: transparent;
    border: none;
    color: var(--medium-text);
    font-size: 1.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.certificate-gallery-close:hover {
    color: var(--light-text);
    transform: rotate(90deg);
    background: rgba(255, 255, 255, 0.05);
}

/* Hackathon Description */
.hackathon-description {
    margin-bottom: 20px;
    padding: 0 5px;
}

.hackathon-description p {
    color: var(--medium-text);
    line-height: 1.6;
    font-size: 1rem;
}

/* Certificate Gallery Grid */
.certificate-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    max-height: calc(90vh - 150px);
    overflow-y: auto;
    padding: 10px 5px;
}

.certificate-gallery-grid::-webkit-scrollbar {
    width: 8px;
}

.certificate-gallery-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.certificate-gallery-grid::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.certificate-gallery-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Certificate Item */
.certificate-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    height: 200px;
}

.certificate-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--primary);
}

.certificate-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.certificate-item:hover .certificate-img {
    transform: scale(1.05);
}

.certificate-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    opacity: 0;
    transition: all 0.3s ease;
}

.certificate-item:hover .certificate-overlay {
    opacity: 1;
}

.certificate-name {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

/* Certificate Lightbox */
.certificate-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 2100;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.certificate-lightbox.active {
    opacity: 1;
    visibility: visible;
}

.certificate-lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.certificate-lightbox-img {
    max-width: 100%;
    max-height: 90vh;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.certificate-lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: transparent;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.certificate-lightbox-close:hover {
    transform: rotate(90deg);
    color: var(--primary);
}

/* Certificate Download Button */
.certificate-lightbox-download {
    position: absolute;
    top: -40px;
    right: 50px;
    background: transparent;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.certificate-lightbox-download:hover {
    color: var(--primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Certificate Lightbox Navigation */
.certificate-lightbox-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
    pointer-events: none;
}

.certificate-lightbox-prev,
.certificate-lightbox-next {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 40px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 3px;
    pointer-events: auto;
}

.certificate-lightbox-prev {
    margin-left: 10px;
}

.certificate-lightbox-next {
    margin-right: 10px;
}

.certificate-lightbox-prev:hover,
.certificate-lightbox-next:hover {
    background: rgba(37, 99, 235, 0.7);
}

.certificate-lightbox-counter {
    position: absolute;
    bottom: -30px;
    left: 0;
    width: 100%;
    text-align: center;
    color: white;
    font-size: 14px;
}

/* View All Certificates Button */
.cert-view-all {
    display: flex;
    justify-content: center;
    margin-top: 25px;
}

.view-all-certificates {
    display: inline-block;
    padding: 10px 20px;
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-light);
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(37, 99, 235, 0.1);
    text-align: center;
}

.view-all-certificates:hover {
    background: rgba(37, 99, 235, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.view-all-certificates i {
    margin-right: 8px;
}

/* Empty State */
.certificate-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--medium-text);
}

.certificate-empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--primary-light);
    opacity: 0.5;
}

.certificate-empty-state p {
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

/* Notification Styles */
.notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: rgba(15, 23, 42, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(100%);
    animation: slideIn 0.3s forwards;
    border-left: 3px solid var(--primary);
    max-width: 300px;
}

.notification.hide {
    animation: slideOut 0.5s forwards;
}

.notification i {
    color: #10b981;
    font-size: 1.2rem;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

@keyframes slideOut {
    to {
        transform: translateX(120%);
        opacity: 0;
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .certificate-gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .certificate-gallery-content {
        padding: 20px;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .certificate-gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .certificate-gallery-title {
        font-size: 1.3rem;
    }

    .certificate-item {
        height: 180px;
    }

    .certificate-lightbox-prev,
    .certificate-lightbox-next {
        width: 30px;
        height: 45px;
        font-size: 0.8rem;
    }

    .certificate-lightbox-counter {
        bottom: -25px;
        font-size: 12px;
    }

    .certificate-lightbox-download {
        top: -35px;
        right: 40px;
        font-size: 16px;
        width: 30px;
        height: 30px;
    }

    .notification-container {
        bottom: 10px;
        right: 10px;
    }

    .notification {
        padding: 10px 15px;
        font-size: 0.9rem;
        max-width: 250px;
    }
}
