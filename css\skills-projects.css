/*
* Skills and Projects CSS for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* ===== SKILLS SECTION ===== */
.skills {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

.skills::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -5%;
    width: 40%;
    height: 70%;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.05), transparent 70%);
    z-index: -1;
    border-radius: 50%;
}

.skills-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
}

.skills-intro p {
    font-size: 1.1rem;
    color: var(--medium-text);
    line-height: 1.8;
}

/* Skills Tabs */
.skills-tabs {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 40px;
}

.skill-tab {
    padding: 10px 20px;
    background: rgba(15, 23, 42, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 30px;
    color: var(--medium-text);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.skill-tab:hover {
    background: rgba(37, 99, 235, 0.1);
    color: var(--light-text);
    transform: translateY(-2px);
}

.skill-tab.active {
    background: rgba(37, 99, 235, 0.2);
    color: var(--primary-light);
    border-color: rgba(37, 99, 235, 0.3);
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
}

.skill-card {
    background: rgba(15, 23, 42, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
    transition: all 0.4s ease;
    height: 100%;
}

.skill-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(37, 99, 235, 0.2);
    background: rgba(30, 41, 59, 0.5);
}

.skill-card-inner {
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.skill-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    font-size: 1.8rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.skill-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    20%, 100% {
        transform: translateX(100%);
    }
}

.skill-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--light-text);
}

.skill-level {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 10px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    border-radius: 10px;
    position: relative;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.skill-percent {
    font-size: 0.9rem;
    color: var(--secondary-light);
    font-weight: 500;
}

/* ===== PROJECTS SECTION ===== */
.projects {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

.projects::before {
    content: '';
    position: absolute;
    bottom: -10%;
    left: -5%;
    width: 40%;
    height: 70%;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.05), transparent 70%);
    z-index: -1;
    border-radius: 50%;
}

.projects-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
}

.projects-intro p {
    font-size: 1.1rem;
    color: var(--medium-text);
    line-height: 1.8;
    margin-bottom: 25px;
}

/* Projects Filter */
.projects-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 40px;
}

.filter-btn {
    padding: 10px 20px;
    background: rgba(15, 23, 42, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 30px;
    color: var(--medium-text);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: rgba(16, 185, 129, 0.1);
    color: var(--light-text);
    transform: translateY(-2px);
}

.filter-btn.active {
    background: rgba(16, 185, 129, 0.2);
    color: var(--secondary-light);
    border-color: rgba(16, 185, 129, 0.3);
}

/* Projects Showcase */
.projects-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

@media (max-width: 480px) {
    .projects-showcase {
        grid-template-columns: 1fr;
        gap: 25px;
    }
}

@media (max-width: 375px) {
    .projects-showcase {
        gap: 20px;
    }
}

.project-item {
    transition: all 0.5s ease;
}

.project-card {
    background: rgba(15, 23, 42, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.4s ease;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(16, 185, 129, 0.2);
}

/* Mobile-specific overrides */
@media (max-width: 480px) {
    .project-card:hover {
        transform: translateY(-5px);
    }
}

.project-image {
    height: 200px;
    position: relative;
    overflow: hidden;
    background-color: var(--background-dark);
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-img-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--background-dark), var(--primary-dark));
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 3rem;
    color: var(--primary-light);
    transition: all 0.5s ease;
}

.project-card:hover .project-img-placeholder {
    transform: scale(1.05);
}

.project-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: top;
    transition: all 0.5s ease;
    display: block;
}

.project-card:hover .project-img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 15px;
}

.project-link {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    transform: translateY(20px);
    opacity: 0;
}

.project-card:hover .project-link {
    transform: translateY(0);
    opacity: 1;
}

.project-card:hover .project-link:nth-child(1) {
    transition-delay: 0.1s;
}

.project-card:hover .project-link:nth-child(2) {
    transition-delay: 0.2s;
}

.project-link:hover {
    background: var(--secondary);
    transform: translateY(-5px);
}

.project-content {
    padding: 25px;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.project-tag {
    padding: 5px 10px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 20px;
    font-size: 0.8rem;
    color: var(--secondary-light);
}

.project-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--light-text);
}

.project-description {
    font-size: 0.95rem;
    color: var(--medium-text);
    line-height: 1.6;
    margin-bottom: 20px;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding-top: 15px;
}

.project-date {
    font-size: 0.85rem;
    color: var(--medium-text);
}

.project-details-btn {
    font-size: 0.9rem;
    color: var(--secondary-light);
    transition: all 0.3s ease;
}

.project-details-btn:hover {
    color: var(--secondary);
    text-decoration: underline;
}

/* Coming Soon Project */
.coming-soon {
    opacity: 0.7;
}

/* Project Modal */
.project-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    background: var(--background-dark);
    margin: 5% auto;
    padding: 30px;
    width: 80%;
    max-width: 1000px;
    border-radius: 12px;
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    color: var(--medium-text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-modal:hover {
    color: var(--light-text);
    transform: rotate(90deg);
}

.modal-body {
    margin-top: 20px;
}

/* Modal Gallery */
.modal-gallery {
    margin-top: 30px;
}

.modal-gallery h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-light);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.gallery-img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.gallery-img:hover {
    transform: scale(1.03);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: var(--primary);
}

.gallery-img::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M15 3h6v6h-2V5h-4V3zM3 3h6v2H5v4H3V3zm16 16v-4h2v6h-6v-2h4zM5 19h4v2H3v-6h2v4z'/%3E%3C/svg%3E");
    background-size: 50%;
    background-repeat: no-repeat;
    background-position: center;
}

.gallery-img:hover::after {
    opacity: 1;
}

.gallery-placeholder {
    background: rgba(15, 23, 42, 0.3);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.modal-tags {
    margin-bottom: 20px;
}

.modal-description ul {
    margin: 15px 0 25px 20px;
}

.modal-description li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 15px;
}

.modal-description li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary);
}
