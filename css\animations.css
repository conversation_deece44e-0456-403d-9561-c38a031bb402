/*
* Animations Stylesheet for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* ===== ABOUT SECTION ===== */
.about {
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -20%;
    width: 60%;
    height: 100%;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.05), transparent 70%);
    z-index: -1;
    border-radius: 50%;
}

.about::after {
    content: '';
    position: absolute;
    bottom: -30%;
    right: -10%;
    width: 50%;
    height: 80%;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.05), transparent 70%);
    z-index: -1;
    border-radius: 50%;
}

.about-wrapper {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 40px;
    margin-top: 30px;
}

/* Profile Card Styles */
.profile-card {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(96, 165, 250, 0.1);
    transition: all 0.4s ease;
    height: fit-content;
}

.profile-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(96, 165, 250, 0.2);
}

.profile-card-header {
    padding: 30px;
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.05));
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
    border: 4px solid rgba(96, 165, 250, 0.3);
    box-shadow: 0 0 25px rgba(37, 99, 235, 0.4);
    position: relative;
}

.profile-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.profile-card:hover .profile-img img {
    transform: scale(1.05);
}

.profile-img::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    box-shadow: inset 0 0 20px rgba(37, 99, 235, 0.3);
    pointer-events: none;
}

.profile-name {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(45deg, var(--primary-light), var(--secondary-light));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 10px rgba(37, 99, 235, 0.2);
}

.profile-title {
    font-size: 1rem;
    color: var(--medium-text);
    font-weight: 500;
    letter-spacing: 1px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-light);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--medium-text);
}

.profile-social {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 25px;
}

.social-btn {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--light-text);
}

.social-btn:hover {
    transform: translateY(-5px);
}

.social-btn.github:hover {
    background: #333;
    color: white;
    border-color: #333;
}

.social-btn.linkedin:hover {
    background: #0077b5;
    color: white;
    border-color: #0077b5;
}

.social-btn.instagram:hover {
    background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
    color: white;
    border-color: #C13584;
}

.social-btn.email:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* About Content Styles */
.about-content {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(96, 165, 250, 0.1);
    transition: all 0.4s ease;
}

.about-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    background: rgba(15, 23, 42, 0.3);
}

.tab-btn {
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: var(--medium-text);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    text-align: center;
}

.tab-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.tab-btn:hover {
    color: var(--light-text);
}

.tab-btn.active {
    color: var(--primary-light);
}

.tab-btn.active::after {
    transform: scaleX(1);
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-pane.active {
    display: block;
}

.content-title {
    font-family: var(--heading-font);
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--primary-light);
    display: flex;
    align-items: center;
    gap: 10px;
}

.content-title i {
    font-size: 1.2rem;
    color: var(--secondary);
}

.tab-pane p {
    margin-bottom: 20px;
    line-height: 1.8;
}

/* Key Skills */
.key-skills {
    margin-top: 30px;
    padding: 20px;
    background: rgba(15, 23, 42, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.key-skills h4 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--light-text);
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skill-tag {
    padding: 8px 15px;
    background: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(96, 165, 250, 0.2);
    border-radius: 30px;
    font-size: 0.9rem;
    color: var(--primary-light);
    transition: all 0.3s ease;
}

.skill-tag:hover {
    background: rgba(37, 99, 235, 0.2);
    transform: translateY(-3px);
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary), var(--secondary));
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-dot {
    position: absolute;
    left: -34px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary);
    border: 2px solid var(--background-dark);
}

.timeline-content {
    background: rgba(15, 23, 42, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateX(5px);
    background: rgba(15, 23, 42, 0.5);
}

.timeline-content h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: var(--light-text);
}

.timeline-date {
    font-size: 0.9rem;
    color: var(--secondary);
    margin-bottom: 10px;
    font-weight: 500;
}

/* Certifications */
.cert-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.cert-item {
    background: rgba(15, 23, 42, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.cert-item:hover {
    transform: translateY(-5px);
    background: rgba(15, 23, 42, 0.5);
}

.cert-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--primary);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.cert-info h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: var(--light-text);
}

.cert-info p {
    font-size: 0.9rem;
    color: var(--medium-text);
    margin-bottom: 0;
}

.cert-link {
    display: inline-block;
    margin-top: 8px;
    color: var(--primary-light);
    font-size: 0.85rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.cert-link:hover {
    color: var(--secondary);
    text-decoration: underline;
}

/* ===== SKILLS SECTION ===== */
.skills-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.skill-category {
    margin-bottom: 30px;
}

.skill-category h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-light);
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    display: inline-flex;
    padding-bottom: 5px;
}

/* Removed the h3::after element that was creating the underline effect */

.skill-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.skill-item {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(96, 165, 250, 0.1);
    border-radius: 12px;
    padding: 20px;
    width: calc(33.333% - 14px);
    min-width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.skill-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.skill-item:hover::before {
    transform: scaleX(1);
}

.skill-item:hover {
    transform: translateY(-10px);
    background: rgba(30, 41, 59, 0.8);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(96, 165, 250, 0.2);
}

.skill-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary), var(--primary-light));
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
    color: white;
}

.skill-item h4 {
    font-weight: 500;
}

/* ===== PROJECTS SECTION ===== */
.projects-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.project-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(96, 165, 250, 0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    pointer-events: none;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(96, 165, 250, 0.2);
}

.project-card:hover::before {
    opacity: 1;
}

.project-img {
    height: 200px;
    overflow: hidden;
}

.project-img-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--background-dark), var(--primary-dark));
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 3rem;
    color: var(--primary-light);
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
}

.project-img-placeholder::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    background: linear-gradient(45deg, transparent, rgba(96, 165, 250, 0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

.project-card:hover .project-img-placeholder {
    transform: scale(1.05);
}

.project-info {
    padding: 25px;
    position: relative;
    z-index: 2;
}

.project-info h3 {
    font-family: var(--heading-font);
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--primary-light);
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

/* Removed the h3::after element that was creating the underline effect */

.project-info p {
    margin-bottom: 20px;
}

.project-links {
    display: flex;
    gap: 15px;
}

.coming-soon {
    opacity: 0.7;
}

/* ===== ACHIEVEMENTS SECTION ===== */
.achievements-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.achievement-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(96, 165, 250, 0.1);
    border-radius: 12px;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary), var(--secondary));
    transform: scaleY(0);
    transform-origin: top;
    transition: transform 0.3s ease;
}

.achievement-card:hover::before {
    transform: scaleY(1);
}

.achievement-card:hover {
    transform: translateY(-10px);
    background: rgba(30, 41, 59, 0.8);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(96, 165, 250, 0.2);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    background: var(--primary);
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.achievement-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    top: -100%;
    left: -100%;
    transition: all 0.5s ease;
}

.achievement-card:hover .achievement-icon::after {
    top: 100%;
    left: 100%;
}

.achievement-card:hover .achievement-icon {
    background: var(--primary-dark);
}

.achievement-info h3 {
    font-family: var(--heading-font);
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--primary-light);
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

/* Removed the h3::after element that was creating the underline effect */

/* ===== CONTACT SECTION ===== */
.contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: var(--primary);
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    color: white;
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
    transition: all 0.3s ease;
}

.contact-item:hover i {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
}

.contact-item h3 {
    font-family: var(--heading-font);
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: var(--primary-light);
    position: relative;
    display: inline-block;
}

.contact-item a {
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

.contact-item a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    background: var(--secondary);
    bottom: -2px;
    left: 0;
    transition: width 0.3s ease;
}

.contact-item a:hover {
    color: var(--secondary);
}

.contact-item a:hover::after {
    width: 100%;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
    background: var(--primary);
    transform: translateY(-5px);
    border-color: var(--primary-light);
}

.contact-form {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(96, 165, 250, 0.1);
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.contact-form:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(96, 165, 250, 0.2);
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(96, 165, 250, 0.1);
    border-radius: 8px;
    color: var(--light-text);
    font-family: var(--main-font);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--medium-text);
    opacity: 0.7;
}

.form-group textarea {
    height: 150px;
    resize: none;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 15px rgba(37, 99, 235, 0.2);
    background: rgba(15, 23, 42, 0.7);
}

/* ===== ANIMATIONS ===== */
@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(153, 153, 153, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(153, 153, 153, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(153, 153, 153, 0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
