/*
* Lightbox CSS for <PERSON><PERSON><PERSON>'s Portfolio
* Author: <PERSON><PERSON><PERSON>
* Version: 1.0
*/

/* Lightbox Container */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

/* Lightbox Content */
.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.lightbox.active .lightbox-content {
    transform: scale(1);
}

/* Lightbox Image */
.lightbox-img {
    display: block;
    max-width: 100%;
    max-height: 90vh;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
}

/* Lightbox Controls */
.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    transform: rotate(90deg);
    color: var(--primary);
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
}

.lightbox-prev,
.lightbox-next {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 40px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.lightbox-prev {
    margin-left: 10px;
}

.lightbox-next {
    margin-right: 10px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(37, 99, 235, 0.7);
}

.lightbox-counter {
    position: absolute;
    bottom: -30px;
    left: 0;
    width: 100%;
    text-align: center;
    color: white;
    font-size: 14px;
}

/* Make gallery images clickable */
.gallery-img {
    cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .lightbox-content {
        max-width: 95%;
    }
    
    .lightbox-prev,
    .lightbox-next {
        width: 30px;
        height: 50px;
    }
}

@media (max-width: 480px) {
    .lightbox-nav {
        position: fixed;
        bottom: 20px;
        top: auto;
        width: auto;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .lightbox-prev,
    .lightbox-next {
        margin: 0 5px;
    }
    
    .lightbox-counter {
        bottom: 80px;
    }
}
